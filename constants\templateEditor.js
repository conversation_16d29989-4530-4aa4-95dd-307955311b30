/**
 * 模板编辑器常量定义
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-06-24
 */

// 模板类型配置
export const TEMPLATE_TYPES = {
	THREE_COLOR: { index: 0, name: '三色桌牌', colors: ['#000000', '#FFFFFF', '#FF0000'] },
	FOUR_COLOR: { index: 1, name: '四色桌牌', colors: ['#000000', '#FFFFFF', '#FF0000', '#FFFF00'] },
	SEVEN_COLOR: { 
		index: 2, 
		name: '七色桌牌', 
		colors: ['#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FFA500'] 
	}
};

// 颜色名称映射
export const COLOR_NAMES = {
	'#000000': '黑色',
	'#FFFFFF': '白色', 
	'#FF0000': '红色',
	'#00FF00': '绿色',
	'#0000FF': '蓝色',
	'#FFFF00': '黄色',
	'#FFA500': '橙色'
};

// 字体配置
export const FONT_FAMILIES = [
	'微软雅黑',
	'宋体', 
	'黑体',
	'Arial',
	'Times New Roman'
];

// 文本对齐选项
export const TEXT_ALIGN_OPTIONS = [
	{ name: '左对齐', value: 'left' },
	{ name: '居中对齐', value: 'center' },
	{ name: '右对齐', value: 'right' }
];

// Canvas配置
export const CANVAS_CONFIG = {
	DEFAULT_WIDTH: 300,
	DEFAULT_HEIGHT: 180,
	MIN_WIDTH: 200,
	MAX_WIDTH: 800,
	MIN_HEIGHT: 120,
	MAX_HEIGHT: 600,
	ASPECT_RATIO: 0.6 // 高度/宽度比例
};

// 字体大小限制
export const FONT_SIZE_LIMITS = {
	MIN: 8,
	MAX: 80,
	DEFAULT: 16,
	STEP: 2
};

// 高度拉伸限制
export const HEIGHT_SCALE_LIMITS = {
	MIN: 0.5,
	MAX: 2.0,
	DEFAULT: 1.0,
	STEP: 0.1
};

// 元素类型
export const ELEMENT_TYPES = {
	TEXT: 'text',
	IMAGE: 'image'
};

// 错误类型
export const ERROR_TYPES = {
	CANVAS_INIT_ERROR: 'CANVAS_INIT_ERROR',
	CANVAS_DRAW_ERROR: 'CANVAS_DRAW_ERROR',
	IMAGE_LOAD_ERROR: 'IMAGE_LOAD_ERROR',
	TEMPLATE_SAVE_ERROR: 'TEMPLATE_SAVE_ERROR',
	VALIDATION_ERROR: 'VALIDATION_ERROR'
};

// 操作图标配置
export const ICON_CONFIG = {
	RADIUS: 18,
	CLICK_RADIUS_MULTIPLIER: 1.5,
	COLORS: {
		EDIT: '#0969da',
		DELETE: '#f44336',
		ROTATE: '#0969da'
	}
};

// 性能配置
export const PERFORMANCE_CONFIG = {
	MAX_FPS: 60,
	FRAME_TIME: 16, // 1000/60
	DEBOUNCE_DELAY: 16,
	MAX_CACHE_SIZE: 100
};

// 默认模板字段
export const DEFAULT_TEMPLATE_FIELDS = {
	name: '张三',
	position: '经理', 
	company: '某某科技有限公司',
	other: ''
};

// 验证规则
export const VALIDATION_RULES = {
	TEMPLATE_NAME: {
		MIN_LENGTH: 1,
		MAX_LENGTH: 50,
		PATTERN: /^[\u4e00-\u9fa5a-zA-Z0-9\s]+$/
	},
	COLOR: {
		PATTERN: /^#[0-9A-Fa-f]{6}$/
	},
	FONT_SIZE: {
		MIN: FONT_SIZE_LIMITS.MIN,
		MAX: FONT_SIZE_LIMITS.MAX
	},
	HEIGHT_SCALE: {
		MIN: HEIGHT_SCALE_LIMITS.MIN,
		MAX: HEIGHT_SCALE_LIMITS.MAX
	}
};

// 消息提示
export const MESSAGES = {
	SUCCESS: {
		TEMPLATE_SAVED: '模板保存成功',
		ELEMENT_DELETED: '元素删除成功',
		OPERATION_COMPLETED: '操作完成'
	},
	ERROR: {
		TEMPLATE_NAME_REQUIRED: '模板名称不能为空',
		INVALID_COLOR: '颜色格式不正确',
		CANVAS_INIT_FAILED: 'Canvas初始化失败',
		IMAGE_LOAD_FAILED: '图片加载失败',
		UNSUPPORTED_COLOR: '当前模板类型不支持该颜色'
	},
	WARNING: {
		ELEMENT_OUT_OF_BOUNDS: '元素超出画布边界',
		PERFORMANCE_WARNING: '元素过多可能影响性能'
	}
};
