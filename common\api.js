import http from '../util/http.js';

/**
 * 获取模板列表
 * @param {Object} params 查询参数
 * @returns {Promise} 
 */
export function fetchTemplates(params) {
  // 返回模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          {
            id: 1,
            name: '经典中国风',
            image: '/static/images/bg.svg',
            background: '/static/images/bg.svg'
          },
          {
            id: 2,
            name: '简约商务',
            image: '/static/images/bg.svg',
            background: '/static/images/bg.svg'
          },
          {
            id: 3,
            name: '科技蓝',
            image: '/static/images/bg.svg',
            background: '/static/images/bg.svg'
          }
        ]
      });
    }, 500);
  });
}

/**
 * 获取设备当前使用的模板
 * @param {String} deviceId 设备ID
 * @returns {Promise}
 */
export function fetchDeviceTemplate(deviceId) {
  // 返回模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          id: 1,
          name: '经典中国风',
          image: '/static/images/bg.svg',
          background: '/static/images/bg.svg'
        }
      });
    }, 300);
  });
}

/**
 * 获取设备信息
 * @param {String} deviceId 设备ID
 * @returns {Promise}
 */
export function fetchDeviceInfo(deviceId) {
  // 返回模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          id: deviceId,
          name: '智能桌牌' + deviceId,
          model: 'E-Display 2023',
          mac: '00:11:22:33:44:55',
          status: 'connected',
          battery: 85,
          signal: 4,
          lastUpdate: '2023-06-15 14:30:22'
        }
      });
    }, 300);
  });
}

/**
 * 保存设备配置
 * @param {Object} config 设备配置
 * @returns {Promise}
 */
export function saveDeviceConfig(config) {
  // 模拟保存
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '保存成功',
        data: true
      });
    }, 800);
  });
}

/**
 * 获取会议室列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function fetchMeetingRooms(params) {
  // 返回模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          totalCount: 10,
          items: [
            {
              id: '1',
              name: '1号会议室',
              status: 'active',
              isBound: true,
              deviceCount: 3
            },
            {
              id: '2',
              name: '2号会议室',
              status: 'inactive',
              isBound: false,
              deviceCount: 0
            }
          ]
        }
      });
    }, 500);
  });
}

export default {
  fetchTemplates,
  fetchDeviceTemplate,
  fetchDeviceInfo,
  saveDeviceConfig,
  fetchMeetingRooms
}; 