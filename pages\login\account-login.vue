<template>
	<view class="container">
		<view class="logo-container">
			<image src="http://*************:8890/i/2025/06/12/logo.png" class="logo" mode="aspectFit"></image>
		</view>

		<view class="form-container">
			<view class="input-group">
				<vol-form :formFields="formData" :formOptions="formOptions" :labelWidth="0" padding="0">
				</vol-form>
			</view>

			<view class="login-btn-container">
				<view class="login-btn" @click="login">
					<text class="login-text">登 录</text>
				</view>
			</view>

			<view class="forgot-password" @click="forgotPassword">
				<text class="forgot-text">忘记密码?</text>
			</view>
		</view>

		<view class="register-container">
			<text class="register-text">还没有账号，现在去 </text>
			<text class="register-link" @click="goRegister">免费注册</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					phone: '',
					password: ''
				},
				formOptions: [
					{
						field: 'phone',
						title: '手机号',
						type: 'text',
						placeholder: '请输入手机号'
					},
					{
						field: 'password',
						title: '密码',
						type: 'password',
						placeholder: '请输入密码'
					}
				]
			}
		},
		methods: {
			login() {
				// 登录逻辑
				console.log('登录', this.formData.phone, this.formData.password);
			},
			forgotPassword() {
				// 跳转到忘记密码页面
				uni.navigateTo({
					url: '/pages/login/forget-password'
				});
			},
			goRegister() {
				// 跳转到注册页面
				uni.navigateTo({
					url: '/pages/login/register'
				});
			}
		}
	}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	background-image: url('http://*************:8890/i/2025/06/12/login-bg.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px;
	box-sizing: border-box;
	overflow: hidden;
}

.logo-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 60px;
	margin-bottom: 50px;
}

.logo {
	width: 120px;
	height: 120px;
}

.form-container {
	flex: 1;
	width: 100%;
	max-width: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.input-group {
	width: 100%;
	margin-bottom: 20px;
}

.login-btn-container {
	width: 100%;
	margin-top: 40px;
	margin-bottom: 30px;
}

.login-btn {
	width: 100%;
	height: 52px;
	background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
	border-radius: 26px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
	border: none;
	transition: all 0.3s ease;
}

.login-btn:active {
	transform: translateY(2px);
	box-shadow: 0 2px 8px rgba(139, 69, 19, 0.4);
}

.login-text {
	color: #FFFFFF;
	font-size: 18px;
	font-weight: 600;
	letter-spacing: 2px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
	position: relative;
	z-index: 10;
}

.forgot-password {
	align-self: center;
	padding: 10px;
	margin-top: 10px;
}

.forgot-text {
	color: #8B4513;
	font-size: 16px;
	font-weight: 500;
	text-decoration: underline;
	text-decoration-color: rgba(139, 69, 19, 0.5);
}

.register-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 14px;
}

.register-text {
	color: #333;
	font-size:  48rpx;
	font-weight: 500;
}

.register-link {
	color: #8B0000;
	font-size: 48rpx;
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}
</style>
