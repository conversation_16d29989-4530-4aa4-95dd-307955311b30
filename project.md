## 项目概述
这是一个基于 uni-app 框架开发的 智能电子桌牌系统 ，主要用于会议室场景的数字化桌牌管理。

## 核心功能
### 1. 会议管理
- 会议室管理 ：支持会议室的创建、编辑、删除和详情查看
- 参会人员管理 ：支持参会人员信息的录入和桌牌绑定
- 桌牌设备管理 ：支持桌牌设备的绑定、解绑和状态管理
- 网关管理 ：支持设备网关的绑定和管理
### 2. 模板系统
- 模板编辑器 ：提供可视化的Canvas编辑器，支持文本和图片元素的添加、编辑
- 多种模板类型 ：支持三色桌牌、四色桌牌、七色桌牌等不同类型
- 模板预览 ：实时预览模板效果
- 模板保存 ：支持模板的本地存储和管理
### 3. 投屏功能
- 单一投屏 ：支持单个设备的内容投屏
- 批量投屏 ：支持多个设备的批量内容推送
- 模板选择 ：支持在投屏时选择不同的显示模板
### 4. 用户系统
- 用户登录/注册 ：支持账号登录、注册和密码找回
- 用户信息管理 ：支持用户信息查看和密码修改
- 权限管理 ：基于Vuex的权限状态管理
## 技术栈
### 前端框架
- uni-app ：跨平台应用开发框架，支持H5、小程序、App等多端发布
- Vue.js 2.x ：基于Vue 2.x版本开发
- uView UI ：基于uni-app的UI组件库
### 状态管理
- Vuex ：用于应用状态管理，包括用户信息、权限等
### 图表组件
- qiun-data-charts ：用于数据可视化图表展示
### 核心技术特性
- Canvas绘图 ：使用Canvas API实现模板编辑器的绘制功能
- 触摸交互 ：支持触摸拖拽、缩放等交互操作
- 本地存储 ：使用uni.storage进行数据本地化存储
- HTTP请求 ：封装了统一的HTTP请求工具
### 开发工具
- HBuilderX ：DCloud官方IDE，专为uni-app开发优化
- Vue DevTools ：Vue开发调试工具
## 项目架构
### 目录结构
- pages/ ：页面文件，包含主页、登录、用户中心等
- components/ ：公共组件，如模板编辑器、Canvas编辑器等
- subPackages/ ：分包页面，包含会议管理、模板管理等功能模块
- utils/ ：工具函数库
- store/ ：Vuex状态管理
- static/ ：静态资源文件
### 核心组件
- CanvasEditor ：Canvas编辑器组件，支持元素的添加、编辑、拖拽等操作
- TemplateEditor ：模板编辑器相关组件集合
- * vol- 系列组件 ：项目自定义的业务组件
## 部署环境
- 开发环境 ： http://127.0.0.1:9991/
- 生产环境 ： https://api.volcore.xyz/
该项目是一个功能完整的企业级电子桌牌管理系统，采用现代化的前端技术栈，具备良好的用户体验和可扩展性。