<template>
	<view class="container"> 
		<view class="form-container"> 
			<view class="input-group">
				<uv-input v-model="username" placeholder="请输入用户名" class="input-field" :border="false"></uv-input>
			</view>
			
			<view class="input-group">
				<uv-input v-model="phone" placeholder="请输入手机号" class="input-field" :border="false"></uv-input>
			</view>
			
			<view class="input-group verification-group">
				<uv-input v-model="verificationCode" placeholder="请输入验证码" class="input-field verification-input" :border="false"></uv-input>
				<view class="get-code-btn" @click="getVerificationCode">
					<text class="get-code-text">获取验证码</text>
				</view>
			</view>
			
			<view class="input-group">
				<uv-input v-model="password" placeholder="请输入密码" type="password" class="input-field" :border="false"></uv-input>
			</view>
			
			<view class="input-group">
				<uv-input v-model="confirmPassword" placeholder="请确认密码" type="password" class="input-field" :border="false"></uv-input>
			</view>
			
			<view class="agreement-container">
				<view class="checkbox-container" @click="toggleAgreement">
					<view class="checkbox" :class="{checked: isAgreed}">
						<text class="checkbox-icon" v-if="isAgreed">✓</text>
					</view>
					<text class="agreement-text">我已阅读并同意</text>
				</view>
				<text class="agreement-link" @click="viewAgreement">《用户协议》</text>
			</view>
			
			<view class="register-btn-container">
				<view class="register-btn" @click="register">
					<text class="register-text">免费注册</text>
				</view>
			</view>
		</view>
		
		<view class="login-container">
			<text class="login-text">已有账号，现在去 </text>
			<text class="login-link" @click="goLogin">立即登录</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				username: '',
				phone: '',
				verificationCode: '',
				password: '',
				confirmPassword: '',
				isAgreed: false,
				codeCountdown: 0,
				codeTimer: null
			}
		},
		computed: {
			codeButtonText() {
				return this.codeCountdown > 0 ? `${this.codeCountdown}s` : '获取验证码';
			}
		},
		methods: {
			getVerificationCode() {
				// 获取验证码逻辑
				if (this.codeCountdown > 0) return;
				
				console.log('获取验证码', this.phone);
				if (!this.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}
				
				// 手机号格式验证
				const phoneReg = /^1[3-9]\d{9}$/;
				if (!phoneReg.test(this.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 这里添加发送验证码的API调用
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				});
				
				// 开始倒计时
				this.startCountdown();
			},
			startCountdown() {
				this.codeCountdown = 60;
				this.codeTimer = setInterval(() => {
					this.codeCountdown--;
					if (this.codeCountdown <= 0) {
						clearInterval(this.codeTimer);
						this.codeTimer = null;
					}
				}, 1000);
			},
			toggleAgreement() {
				this.isAgreed = !this.isAgreed;
			},
			viewAgreement() {
				// 跳转到用户协议页面
				uni.navigateTo({
					url: '/pages/user/agreement'
				});
			},
			register() {
				// 注册逻辑
				console.log('注册', this.username, this.phone, this.password, this.confirmPassword, this.verificationCode);
				
				// 表单验证
				if (!this.username || !this.phone || !this.password || !this.confirmPassword || !this.verificationCode) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					});
					return;
				}
				
				if (this.password !== this.confirmPassword) {
					uni.showToast({
						title: '两次密码输入不一致',
						icon: 'none'
					});
					return;
				}
				
				if (this.password.length < 6) {
					uni.showToast({
						title: '密码长度不能少于6位',
						icon: 'none'
					});
					return;
				}
				
				if (!this.isAgreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					});
					return;
				}
				
				// 这里添加注册的API调用
				uni.showToast({
					title: '注册成功',
					icon: 'success'
				});
				
				// 延迟跳转到登录页面
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			},
			goLogin() {
				// 返回登录页面
				uni.navigateBack();
			}
		},
		beforeDestroy() {
			if (this.codeTimer) {
				clearInterval(this.codeTimer);
			}
		}
	}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start; /* 修改为flex-start，避免space-between导致的布局问题 */
	height: 100vh;
	background-image: url('http://*************:8890/i/2025/06/12/layout_ornament.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 0 20px 30px 20px; /* 增加底部内边距，确保底部内容可见 */
	box-sizing: border-box;
	overflow: auto; /* 修改为auto，允许内容超出时显示滚动条 */
}

.logo-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 10px;
	margin-bottom: 15px;
}

.logo {
	width: 60px;
	height: 60px;
}

.form-container {
	flex: 1;
	width: 100%;
	max-width: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.title-container {
	margin-bottom: 25px;
}

.title-text {
	color: #2C1810;
	font-size: 26px;
	font-weight: 900;
	letter-spacing: 4px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3), 1px 1px 2px rgba(139,69,19,0.4);
	text-align: center;
}

.input-group {
	width: 100%;
	margin-bottom: 18px;
	position: relative;
}

.verification-group {
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: 10px;
}

.verification-input {
	flex: 1;
}

.input-field {
	width: 100%;
	height: 48px;
	background-color: rgba(255, 255, 255, 0.15);
	border-bottom: 2px solid rgba(139, 69, 19, 0.6);
	color: #000;
	font-size: 15px;
	padding: 0 15px;
	border-radius: 0;
	font-weight: 500;
}

.input-field::placeholder {
	color: #333 !important;
	font-weight: 600;
}

.input-field::-webkit-input-placeholder {
	color: #333 !important;
	font-weight: 600;
}

.input-field::-moz-placeholder {
	color: #333 !important;
	font-weight: 600;
}

.input-field:-ms-input-placeholder {
	color: #333 !important;
	font-weight: 600;
}

.get-code-btn {
	width: 100px;
	height: 48px;
	background: linear-gradient(135deg, #8B4513, #CD853F, #DEB887);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	box-shadow: inset 0 2px 4px rgba(0,0,0,0.2), 0 2px 6px rgba(0,0,0,0.15);
	border: 1px solid rgba(139, 69, 19, 0.6);
	transition: all 0.3s ease;
}

.get-code-btn:active {
	transform: scale(0.95);
	box-shadow: inset 0 4px 8px rgba(0,0,0,0.3);
}

.get-code-text {
	color: #2C1810;
	font-size: 11px;
	font-weight: 700;
	letter-spacing: 1px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
}

.agreement-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-bottom: 25px;
	width: 100%;
	justify-content: center;
	flex-wrap: wrap;
	gap: 5px;
}

.checkbox-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: 8px;
}

.checkbox {
	width: 18px;
	height: 18px;
	border: 2px solid #8B4513;
	border-radius: 3px;
	background-color: rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.checkbox.checked {
	background-color: #8B4513;
	border-color: #8B4513;
}

.checkbox-icon {
	color: #fff;
	font-size: 12px;
	font-weight: bold;
}

.agreement-text {
	color: #333;
	font-size: 13px;
	font-weight: 500;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
}

.agreement-link {
	color: #8B4513;
	font-size: 13px;
	font-weight: bold;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
	text-decoration: underline;
}

.register-btn-container {
	width: 100%;
	margin-top: 25px;
	margin-bottom: 10px;
}

.register-btn {
	width: 100%;
	height: 50px;
	background: linear-gradient(135deg, #2C1810, #8B4513, #CD853F);
	border-radius: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	clip-path: polygon(5% 0%, 95% 0%, 98% 20%, 100% 50%, 98% 80%, 95% 100%, 5% 100%, 2% 80%, 0% 50%, 2% 20%);
	box-shadow: inset 0 2px 4px rgba(0,0,0,0.3), 0 4px 8px rgba(0,0,0,0.2);
	border: 1px solid rgba(139, 69, 19, 0.8);
}

.register-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(205,133,63,0.4), transparent);
	transition: left 0.8s ease-in-out;
}

.register-btn:active::before {
	left: 100%;
}

.register-btn::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(ellipse at 20% 30%, rgba(139,69,19,0.6) 0%, transparent 50%),
	            radial-gradient(ellipse at 80% 70%, rgba(205,133,63,0.4) 0%, transparent 40%),
	            radial-gradient(ellipse at 50% 10%, rgba(222,184,135,0.3) 0%, transparent 30%);
	pointer-events: none;
}

.register-text {
	color: #F5F5DC;
	font-size: 18px;
	font-weight: 900;
	letter-spacing: 3px;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 1px 1px 2px rgba(139,69,19,0.6);
	position: relative;
	z-index: 10;
	transform: scale(1.05);
	filter: drop-shadow(0 0 3px rgba(245,245,220,0.3));
}

.login-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 20px; /* 添加顶部边距，确保与上方内容有足够间距 */
	margin-bottom: 10px; /* 添加底部边距，确保不被滚动条隐藏 */
}

.login-text {
	color: #333;
	font-size: 14px;
	font-weight: 500;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
}

.login-link {
	color: #8B4513;
	font-size: 14px;
	font-weight: bold;
	font-family: 'STKaiti', 'KaiTi', '楷体', serif;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
	letter-spacing: 1px;
}
</style>
