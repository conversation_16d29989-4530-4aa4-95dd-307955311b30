.peng-tree-mask {
  position: fixed;
  top: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 9998;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: all 0.3s ease;
  visibility: hidden;
}
.peng-tree-mask.show {
  visibility: visible;
  opacity: 1;
}
.peng-tree-cnt {
  position: fixed;
  top: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 9999;
  top: 360rpx;
  transition: all 0.3s ease;
  transform: translateY(100%);
}
.peng-tree-cnt.show {
  transform: translateY(0);
}
.peng-tree-bar {
  background-color: #fff;
  height: 72rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-bottom-width: 1rpx !important;
  border-bottom-style: solid;
  border-bottom-color: #f5f5f5;
  font-size: 32rpx;
  color: #757575;
  line-height: 1;
}
.peng-tree-bar-confirm {
  color: #007aff;
}
.peng-tree-view {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  top: 72rpx;
  background-color: #fff;
  padding-top: 20rpx;
  padding-right: 20rpx;
  padding-bottom: 20rpx;
  padding-left: 20rpx;
}
.peng-tree-view-sc {
  height: 100%;
  overflow: hidden;
}
.peng-tree-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26rpx;
  color: #757575;
  line-height: 1;
  height: 0;
  opacity: 0;
  transition: 0.2s;
  position: relative;
  overflow: hidden;
}
.peng-tree-item.show {
  height: 80rpx;
  opacity: 1;
}
.peng-tree-item.showchild:before {
  transform: rotate(90deg);
}
.peng-tree-item.last:before {
  opacity: 0;
}
.peng-tree-icon {
  width: 26rpx;
  height: 26rpx;
  margin-right: 8rpx;
}
.peng-tree-label {
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  line-height: 1.2;
}
.peng-tree-check {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.peng-tree-check-yes,
.peng-tree-check-no {
  width: 20px;
  height: 20px;
  border-top-left-radius: 20%;
  border-top-right-radius: 20%;
  border-bottom-right-radius: 20%;
  border-bottom-left-radius: 20%;
  border-top-width: 1rpx;
  border-left-width: 1rpx;
  border-bottom-width: 1rpx;
  border-right-width: 1rpx;
  border-style: solid;
  border-color: #007aff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}
.peng-tree-check-yes-b {
  width: 12px;
  height: 12px;
  border-top-left-radius: 20%;
  border-top-right-radius: 20%;
  border-bottom-right-radius: 20%;
  border-bottom-left-radius: 20%;
  background-color: #007aff;
}
.peng-tree-check .radio {
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.peng-tree-check .radio .peng-tree-check-yes-b {
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.hover-c {
  opacity: 0.6;
}
