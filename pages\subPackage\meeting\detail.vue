<template>
	<view class="container">
		<!-- 主内容区域 -->
		<view class="content">
			<view class="detail-card">
				<!-- 会议室基本信息 -->
				<view class="room-header">
					<view class="room-icon">
						<view class="status-dot" :class="{active: roomInfo.status === 'active'}"></view>
						<image class="icon-image" src="/static/images/logo_icon.png" mode="aspectFit"></image>
					</view>
					<view class="room-title">
						<text class="room-name">{{ roomInfo.name }}</text>
						<text class="room-status">{{ roomInfo.status === 'active' ? '已启用' : '已禁用' }}</text>
					</view>
				</view>
				
				<!-- 会议室详细信息 -->
				<view class="info-section">
					<view class="info-title">
						<text>设备信息</text>
					</view>
					
					<view class="info-list">
						<view class="info-item">
							<text class="info-label">型号</text>
							<text class="info-value">{{ roomInfo.model || '未绑定' }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">版本</text>
							<text class="info-value">{{ roomInfo.version || '未绑定' }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">类型</text>
							<text class="info-value">{{ roomInfo.type || '未绑定' }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">编号</text>
							<text class="info-value">{{ roomInfo.code || '未绑定' }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">频道</text>
							<text class="info-value">{{ roomInfo.channel || '未绑定' }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">创建时间</text>
							<text class="info-value">{{ roomInfo.createTime || '未知' }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">最后更新</text>
							<text class="info-value">{{ roomInfo.updateTime || '未知' }}</text>
						</view>
					</view>
				</view>
				
				<!-- 设备绑定状态 -->
				<view class="binding-section">
					<view class="binding-status" :class="{bound: roomInfo.isBound}">
						<image class="binding-icon" :src="roomInfo.isBound ? '/static/images/gateway_binding.png' : '/static/images/gateway_unbinding.png'" mode="aspectFit"></image>
						<text class="binding-text">{{ roomInfo.isBound ? '已绑定设备' : '未绑定设备' }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
		<view class="footer">
			<view class="button-group">
				<view class="action-button edit" @click="editRoom">
					<text>编辑</text>
				</view>
				<view class="action-button bind" @click="bindDevice" v-if="!roomInfo.isBound">
					<text>绑定设备</text>
				</view>
				<view class="action-button unbind" @click="unbindDevice" v-if="roomInfo.isBound">
					<text>解绑设备</text>
				</view>
				<view class="action-button delete" @click="deleteRoom">
					<text>删除</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				roomId: '',
				roomInfo: {
					id: '',
					name: '',
					model: '',
					version: '',
					type: '',
					code: '',
					channel: '',
					status: 'active',
					isBound: false,
					createTime: '2023-05-20 10:30:00',
					updateTime: '2023-05-20 10:30:00'
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.roomId = options.id;
				// 获取会议室详情
				this.getRoomDetail(options.id);
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 获取会议室详情
			getRoomDetail(id) {
				// 这里应该调用API获取会议室详情
				// 目前使用模拟数据
				const mockData = {
					'1': {
						id: '1',
						name: '1号会议室',
						model: '未绑定',
						version: '未绑定',
						type: '未绑定',
						code: '未绑定',
						channel: '未绑定',
						status: 'active',
						isBound: false,
						createTime: '2023-05-20 10:30:00',
						updateTime: '2023-05-20 10:30:00'
					},
					'2': {
						id: '2',
						name: '121212',
						model: '未绑定',
						version: '未绑定',
						type: '未绑定',
						code: '未绑定',
						channel: '未绑定',
						status: 'inactive',
						isBound: false,
						createTime: '2023-05-21 14:20:00',
						updateTime: '2023-05-21 14:20:00'
					}
				};
				
				if (mockData[id]) {
					// this.roomInfo = { ...mockData[id] };
					// 使用传统方式复制对象
					const roomData = mockData[id];
					for (let key in roomData) {
						if (roomData.hasOwnProperty(key)) {
							this.roomInfo[key] = roomData[key];
						}
					}
				} else {
					uni.showToast({
						title: '会议室不存在',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			},
			
			// 编辑会议室
			editRoom() {
				uni.navigateTo({
					url: `/pages/meeting/edit?id=${this.roomId}`
				});
			},
			
			// 绑定设备
			bindDevice() {
				uni.showToast({
					title: '正在绑定设备...',
					icon: 'loading',
					duration: 2000
				});
				
				// 模拟绑定过程
				setTimeout(() => {
					this.roomInfo.isBound = true;
					this.roomInfo.model = 'E-Link 2000';
					this.roomInfo.version = 'v1.0.5';
					this.roomInfo.type = '电子桌牌';
					this.roomInfo.code = 'DP' + Math.floor(Math.random() * 10000);
					this.roomInfo.channel = 'CH-' + Math.floor(Math.random() * 100);
					this.roomInfo.updateTime = new Date().toLocaleString();
					
					uni.showToast({
						title: '设备绑定成功',
						icon: 'success'
					});
				}, 2000);
			},
			
			// 解绑设备
			unbindDevice() {
				uni.showModal({
					title: '确认解绑',
					content: '确定要解绑当前设备吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '正在解绑设备...',
								icon: 'loading',
								duration: 1500
							});
							
							// 模拟解绑过程
							setTimeout(() => {
								this.roomInfo.isBound = false;
								this.roomInfo.model = '未绑定';
								this.roomInfo.version = '未绑定';
								this.roomInfo.type = '未绑定';
								this.roomInfo.code = '未绑定';
								this.roomInfo.channel = '未绑定';
								this.roomInfo.updateTime = new Date().toLocaleString();
								
								uni.showToast({
									title: '设备解绑成功',
									icon: 'success'
								});
							}, 1500);
						}
					}
				});
			},
			
			// 删除会议室
			deleteRoom() {
				uni.showModal({
					title: '确认删除',
					content: `确定要删除${this.roomInfo.name}吗？`,
					success: (res) => {
						if (res.confirm) {
							// 这里应该调用API删除会议室
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
							
							// 返回列表页
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
.container {
	flex: 1;
	background-color: #f5f5f5;
	background-image: url('/static/images/bg.svg');
	background-size: cover;
	background-position: center;
}

.content {
	padding: 30rpx;
	flex: 1;
	padding-bottom: 150rpx; /* 为底部按钮留出空间 */
}

/* 详情卡片样式 */
.detail-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 会议室头部信息 */
.room-header {
	padding: 30rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	border-bottom: 1px solid #f0f0f0;
}

.room-icon {
	width: 120rpx;
	height: 120rpx;
	position: relative;
	margin-right: 30rpx;
}

.status-dot {
	position: absolute;
	top: 0;
	left: 0;
	width: 20rpx;
	height: 20rpx;
	border-radius: 10rpx;
	background-color: #cccccc;
}

.status-dot.active {
	background-color: #8B0000;
}

.icon-image {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	background-color: #8B0000;
}

.room-title {
	flex: 1;
}

.room-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.room-status {
	font-size: 24rpx;
	color: #8B0000;
	background-color: rgba(139, 0, 0, 0.1);
	padding: 4rpx 16rpx;
	border-radius: 20rpx;
}

/* 信息部分样式 */
.info-section {
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
}

.info-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.info-list {
	display: flex;
	flex-direction: column;
}

.info-item {
	display: flex;
	flex-direction: row;
	margin-bottom: 15rpx;
	align-items: center;
}

.info-label {
	width: 150rpx;
	font-size: 28rpx;
	color: #999;
}

.info-value {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

/* 绑定状态部分 */
.binding-section {
	padding: 30rpx;
}

.binding-status {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
}

.binding-status.bound {
	background-color: rgba(139, 0, 0, 0.1);
}

.binding-icon {
	width: 60rpx;
	height: 60rpx;
	margin-right: 20rpx;
}

.binding-text {
	font-size: 28rpx;
	color: #666;
}

.binding-status.bound .binding-text {
	color: #8B0000;
}

/* 底部按钮样式 */
.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: transparent;
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: center;
}

.button-group {
	display: flex;
	flex-direction: row;
	width: 100%;
	justify-content: space-between;
}

.action-button {
	height: 90rpx;
	border-radius: 45rpx;
	justify-content: center;
	align-items: center;
	display: flex;
	padding: 0 40rpx;
}

.action-button text {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: bold;
}

.action-button.edit {
	background-color: #8B0000; /* 深红色 */
	box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
}

.action-button.bind {
	background-color: #8B0000; /* 深红色 */
	box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
}

.action-button.unbind {
	background-color: #666;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
}

.action-button.delete {
	background-color: #ff6b6b;
	box-shadow: 0 4rpx 10rpx rgba(255, 107, 107, 0.3);
}
</style>