<template>
	<view class="container">
		<!-- 会议室信息头部 -->
		<view class="room-header">
			<view class="room-info" @click="showRoomSelector">
				<text class="room-name">会议室名称：{{ roomName }}</text>
				<!-- <text class="switch-hint">点击切换</text> -->
			</view>
			<view class="select-all" v-if="hasPeople">
				<checkbox :checked="isAllSelected" @click="toggleSelectAll" color="#8B0000" />
				<text class="select-all-text">全选</text>
			</view>
		</view>
		
		<!-- 无人员状态 -->
		<view class="empty-state" v-if="!hasPeople">			
			<view class="empty-content">
				<view class="action-button add-person" @click="addPerson">
					<image class="button-bg" src="http://14.103.146.84:8890/i/2025/06/12/btn.png" mode="aspectFill"></image>
					<text class="action-text">新增人员</text>
				</view>
				<view class="action-button batch-import" @click="batchImport">
					<image class="button-bg" src="http://14.103.146.84:8890/i/2025/06/12/btn.png" mode="aspectFill"></image>
					<text class="action-text">批量导入人员</text>
				</view>
			</view>
		</view>
		
		<!-- 有人员状态 -->
		<view class="people-list" v-if="hasPeople">
			<!-- 人员项 -->
			<view class="person-item" v-for="(person, index) in peopleList" :key="index">
				<view class="person-header">
					<text class="desk-number">桌牌编号：{{ person.deskNumber }}</text>
					<checkbox :checked="person.selected" @click="togglePersonSelect(person)" color="#8B0000" />
				</view>
				<view class="person-details">
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">姓名：</text>
							<text class="detail-value">{{ person.name }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">职称：</text>
							<text class="detail-value">{{ person.title }}</text>
						</view>
					</view>
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">工号：</text>
							<text class="detail-value">{{ person.employeeId }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">公司名称：</text>
							<text class="detail-value">{{ person.company }}</text>
						</view>
					</view>
				</view>
				<view class="person-actions">
					<view class="action-btn delete-btn" @click="deletePerson(person)">
						<text>删除人员</text>
					</view>
					<view class="action-btn edit-btn" @click="editPerson(person)">
						<text>编辑人员</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 浮动按钮 -->
		<view class="floating-buttons" v-if="hasPeople">
			<!-- 添加按钮 -->
			<view class="float-btn add-btn" @click="addPerson">
				<text class="float-btn-text">+</text>
			</view>
			<!-- Excel导入按钮 -->
			<view class="float-btn excel-btn" @click="importExcel">
				<text class="excel-text">EXL</text>
			</view>
		</view>
		
		<!-- 底部删除选中按钮 -->
		<view class="bottom-action" v-if="hasPeople && hasSelected">
			<view class="delete-selected-btn" @click="deleteSelected">
				<text>删除选中</text>
			</view>
		</view>
		
		<!-- 桌牌选择弹窗 -->
		<view class="desk-modal" v-if="showDeskModal" @click="closeDeskModal">
			<view class="desk-modal-content" @click.stop="stopPropagation">
				<view class="modal-header">
					<text class="modal-title">选择桌牌编号</text>
					<text class="close-btn" @click="closeDeskModal">×</text>
				</view>
				<view class="desk-list">
					<view 
						v-for="desk in availableDeskList" 
						:key="desk.id" 
						class="desk-option" 
						:class="{disabled: desk.status !== 'available'}"
						@click="selectDesk(desk)"
					>
						<view class="desk-info">
							<text class="desk-number">{{ desk.number }}</text>
							<text class="desk-gateway">{{ desk.gatewayName }}</text>
							<text class="desk-status" :class="desk.status">{{ getStatusText(desk.status) }}</text>
						</view>
						<view class="desk-check" v-if="currentPerson.deskNumber === desk.number">✓</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 会议室选择弹窗 -->
		<view class="room-modal" v-if="showRoomModal" @click="closeRoomModal">
			<view class="room-modal-content" @click.stop="stopPropagation">
				<view class="modal-header">
					<text class="modal-title">选择会议室</text>
					<view class="close-btn" @click="closeRoomModal">
						<text>×</text>
					</view>
				</view>
				<view class="room-list">
					<view class="room-option" v-for="room in roomList" :key="room.id" @click="selectRoom(room)">
						<text class="room-option-name">{{ room.name }}</text>
						<view class="room-option-check" v-if="room.name === roomName">
							<text>✓</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 新增/编辑人员弹窗 -->
		<view class="person-modal" v-if="showPersonModal" @click="closePersonModal">
			<view class="person-modal-content" @click.stop="stopPropagation">
				<view class="modal-header">
					<text class="modal-title">{{ personModalType === 'add' ? '新增人员' : '编辑人员' }}</text>
					<view class="close-btn" @click="closePersonModal">
						<text>×</text>
					</view>
				</view>
				<view class="person-form">
					<view class="form-item">
						<text class="form-label">桌牌编号：</text>
						<view class="form-select" @click="showDeskSelector">
							<text class="select-text" :class="{placeholder: !currentPerson.deskNumber}">
								{{ currentPerson.deskNumber || '请选择桌牌编号' }}
							</text>
							<text class="select-arrow">▼</text>
						</view>
					</view>
					<view class="form-item">
						<text class="form-label">姓名</text>
						<input class="form-input" v-model="currentPerson.name" placeholder="例如：姓名" />
					</view>
					<view class="form-item">
						<text class="form-label">工号</text>
						<input class="form-input" v-model="currentPerson.employeeId" placeholder="例如：编号" />
					</view>
					<view class="form-item">
						<text class="form-label">职称</text>
						<input class="form-input" v-model="currentPerson.title" placeholder="例如：职称" />
					</view>
					<view class="form-item">
						<text class="form-label">公司名称</text>
						<input class="form-input" v-model="currentPerson.company" placeholder="例如：公司" />
					</view>
				</view>
				<view class="modal-actions">
					<view class="action-btn cancel-btn" @click="closePersonModal">
						<text>取消</text>
					</view>
					<view class="action-btn confirm-btn" @click="confirmPerson">
						<text>确定</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				roomName: '1号会议室',
				hasPeople: true, // 控制显示状态，true显示人员列表，false显示空状态
				showRoomModal: false, // 控制会议室选择弹窗
				roomList: [ // 会议室列表
					{ id: 1, name: '1号会议室' },
					{ id: 2, name: '2号会议室' },
					{ id: 3, name: '3号会议室' },
					{ id: 4, name: '4号会议室' },
					{ id: 5, name: '5号会议室' },
					{ id: 6, name: '6号会议室' },
					{ id: 7, name: '7号会议室' },
					{ id: 8, name: '8号会议室' }
				],
				showPersonModal: false, // 控制新增/编辑人员弹窗
				personModalType: 'add', // 弹窗类型：add新增，edit编辑
				showDeskModal: false, // 控制桌牌选择弹窗
				currentPerson: { // 当前编辑的人员信息
					id: null,
					deskNumber: '',
					name: '',
					title: '',
					employeeId: '',
					company: '',
					selected: false
				},
				// 桌牌设备列表（模拟数据，实际应从API获取）
				deskDeviceList: [
					{ id: 1, number: '001', gatewayId: 'GW001', gatewayName: '网关1', status: 'available', boundPersonId: null },
					{ id: 2, number: '002', gatewayId: 'GW001', gatewayName: '网关1', status: 'bound', boundPersonId: 1 },
					{ id: 3, number: '003', gatewayId: 'GW001', gatewayName: '网关1', status: 'available', boundPersonId: null },
					{ id: 4, number: '004', gatewayId: 'GW002', gatewayName: '网关2', status: 'available', boundPersonId: null },
					{ id: 5, number: '005', gatewayId: 'GW002', gatewayName: '网关2', status: 'available', boundPersonId: null },
					{ id: 6, number: '006', gatewayId: 'GW003', gatewayName: '网关3', status: 'offline', boundPersonId: null },
					{ id: 7, number: '007', gatewayId: 'GW003', gatewayName: '网关3', status: 'available', boundPersonId: null }
				],
				peopleList: [
					{
						id: 1,
						deskNumber: '12121',
						name: '21212',
						title: '2121',
						employeeId: '2121',
						company: '2121',
						selected: false
					}
				]
			}
		},
		computed: {
			isAllSelected() {
				return this.peopleList.length > 0 && this.peopleList.every(person => person.selected);
			},
			hasSelected() {
				return this.peopleList.some(person => person.selected);
			},
			// 可用的桌牌列表（根据网关限制和绑定状态过滤）
			availableDeskList() {
				return this.deskDeviceList.filter(desk => {
					// 如果是编辑模式且当前桌牌已绑定给当前人员，则显示
					if (this.personModalType === 'edit' && desk.boundPersonId === this.currentPerson.id) {
						return true;
					}
					// 只显示可用状态的桌牌
					return desk.status === 'available';
				});
			}
		},
		onLoad(options) {
			// 获取传递的会议室信息
			if (options.roomName) {
				this.roomName = decodeURIComponent(options.roomName);
			}
			// 模拟数据加载
			this.loadPeopleData();
		},
		methods: {
			// 阻止事件冒泡
			stopPropagation() {
				// 空函数，用于阻止事件冒泡
			},

			// 加载人员数据
			loadPeopleData() {
				// 这里可以根据实际需求调用API
				// 如果没有人员数据，设置hasPeople为false
				// this.hasPeople = this.peopleList.length > 0;
			},
			
			// 切换全选状态
			toggleSelectAll() {
				const newState = !this.isAllSelected;
				this.peopleList.forEach(person => {
					person.selected = newState;
				});
			},
			
			// 切换单个人员选择状态
			togglePersonSelect(person) {
				person.selected = !person.selected;
			},
			
			// 新增人员
			addPerson() {
				console.log('新增人员');
				this.personModalType = 'add';
				this.currentPerson = {
					id: null,
					deskNumber: '',
					name: '',
					title: '',
					employeeId: '',
					company: '',
					selected: false
				};
				this.showPersonModal = true;
			},
			
			// 批量导入人员
			batchImport() {
				console.log('批量导入人员');
				uni.showToast({
					title: '批量导入功能开发中',
					icon: 'none'
				});
			},
			
			// 编辑人员
			editPerson(person) {
				console.log('编辑人员:', person);
				this.personModalType = 'edit';
				// this.currentPerson = { ...person };
				// 使用传统方式复制对象
				this.currentPerson = {
					id: null,
					deskNumber: '',
					name: '',
					title: '',
					employeeId: '',
					company: '',
					selected: false
				};
				for (let key in person) {
					if (person.hasOwnProperty(key)) {
						this.currentPerson[key] = person[key];
					}
				}
				this.showPersonModal = true;
			},
			
			// 删除人员
			deletePerson(person) {
				uni.showModal({
					title: '确认删除',
					content: `确定要删除${person.name}吗？`,
					success: (res) => {
						if (res.confirm) {
							this.peopleList = this.peopleList.filter(p => p.id !== person.id);
							// 如果删除后没有人员了，切换到空状态
							if (this.peopleList.length === 0) {
								this.hasPeople = false;
							}
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 删除选中的人员
			deleteSelected() {
				const selectedPeople = this.peopleList.filter(person => person.selected);
				if (selectedPeople.length === 0) {
					uni.showToast({
						title: '请先选择要删除的人员',
						icon: 'none'
					});
					return;
				}
				
				uni.showModal({
					title: '确认删除',
					content: `确定要删除选中的${selectedPeople.length}个人员吗？`,
					success: (res) => {
						if (res.confirm) {
							this.peopleList = this.peopleList.filter(person => !person.selected);
							// 如果删除后没有人员了，切换到空状态
							if (this.peopleList.length === 0) {
								this.hasPeople = false;
							}
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// Excel导入
			importExcel() {
				console.log('Excel导入');
				uni.showToast({
					title: 'Excel导入功能开发中',
					icon: 'none'
				});
			},
			
			// 显示桌牌选择器
			showDeskSelector() {
				this.showDeskModal = true;
			},
			
			// 选择桌牌
			selectDesk(desk) {
				if (desk.status !== 'available') {
					uni.showToast({
						title: '该桌牌不可用',
						icon: 'none'
					});
					return;
				}
				
				// 检查网关限制：同一网关下只能绑定一个参会人员
				const sameGatewayBoundDesk = this.deskDeviceList.find(d => 
					d.gatewayId === desk.gatewayId && 
					d.status === 'bound' && 
					d.boundPersonId !== this.currentPerson.id
				);
				
				if (sameGatewayBoundDesk) {
					uni.showModal({
						title: '网关限制',
						content: `${desk.gatewayName}下已有其他人员绑定桌牌，同一网关下只能绑定一个参会人员，不可跨网关使用。`,
						showCancel: false
					});
					return;
				}
				
				this.currentPerson.deskNumber = desk.number;
				this.closeDeskModal();
			},
			
			// 关闭桌牌选择弹窗
			closeDeskModal() {
				this.showDeskModal = false;
			},
			
			// 获取桌牌状态文本
			getStatusText(status) {
				switch(status) {
					case 'available': return '可用';
					case 'bound': return '已绑定';
					case 'offline': return '离线';
					default: return '未知';
				}
			},
			
			// 更新桌牌设备绑定状态
			updateDeskBinding(deskNumber, personId) {
				const desk = this.deskDeviceList.find(d => d.number === deskNumber);
				if (desk) {
					if (personId) {
						// 绑定桌牌
						desk.status = 'bound';
						desk.boundPersonId = personId;
					} else {
						// 解绑桌牌
						desk.status = 'available';
						desk.boundPersonId = null;
					}
				}
			},
			
			// 显示会议室选择器
			showRoomSelector() {
				this.showRoomModal = true;
			},
			
			// 选择会议室
			selectRoom(room) {
				this.roomName = room.name;
				this.showRoomModal = false;
				// 切换会议室后重新加载数据
				this.loadPeopleData();
				uni.showToast({
					title: `已切换到${room.name}`,
					icon: 'success'
				});
			},
			
			// 关闭会议室选择弹窗
			closeRoomModal() {
				this.showRoomModal = false;
			},
			
			// 关闭人员弹窗
			closePersonModal() {
				this.showPersonModal = false;
			},
			
			// 确认新增/编辑人员
			confirmPerson() {
				// 验证必填字段
				if (!this.currentPerson.deskNumber || !this.currentPerson.name || 
					!this.currentPerson.title || !this.currentPerson.employeeId || 
					!this.currentPerson.company) {
					uni.showToast({
						title: '请填写完整信息',
						icon: 'none'
					});
					return;
				}
				
				if (this.personModalType === 'add') {
				// 新增人员
				const newPerson = {
					id: Date.now(), // 简单的ID生成
					deskNumber: this.currentPerson.deskNumber,
					name: this.currentPerson.name,
					title: this.currentPerson.title,
					employeeId: this.currentPerson.employeeId,
					company: this.currentPerson.company,
					selected: false
				};
				this.peopleList.push(newPerson);
				this.hasPeople = true;
				
				// 更新桌牌设备绑定状态
				if (newPerson.deskNumber) {
					this.updateDeskBinding(newPerson.deskNumber, newPerson.id);
				}
				
				uni.showToast({
					title: '新增成功',
					icon: 'success'
				});
			} else {
				// 编辑人员
				const index = this.peopleList.findIndex(p => p.id === this.currentPerson.id);
				if (index !== -1) {
					const oldPerson = this.peopleList[index];
					
					// 如果桌牌编号发生变化，需要更新绑定状态
					if (oldPerson.deskNumber !== this.currentPerson.deskNumber) {
						// 解绑旧桌牌
						if (oldPerson.deskNumber) {
							this.updateDeskBinding(oldPerson.deskNumber, null);
						}
						// 绑定新桌牌
						if (this.currentPerson.deskNumber) {
							this.updateDeskBinding(this.currentPerson.deskNumber, this.currentPerson.id);
						}
					}
					
					this.peopleList[index] = {
						id: this.currentPerson.id,
						deskNumber: this.currentPerson.deskNumber,
						name: this.currentPerson.name,
						title: this.currentPerson.title,
						employeeId: this.currentPerson.employeeId,
						company: this.currentPerson.company,
						selected: this.currentPerson.selected
					};
					uni.showToast({
						title: '编辑成功',
						icon: 'success'
					});
				}
			}
				
				this.showPersonModal = false;
			}
		}
	}
</script>

<style lang="scss">
.container {
	flex: 1;
	display: flex;
	flex-direction: column;
	// padding-bottom: 160rpx; 
}

/* 会议室信息头部 */
.room-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
}

.room-info {
	flex: 1;
	cursor: pointer;
	padding: 10rpx;
	border-radius: 8rpx;
	transition: background-color 0.3s;
}

.room-info:hover {
	background-color: #f5f5f5;
}

.room-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
}

.switch-hint {
	font-size: 24rpx;
	color: #999999;
	margin-top: 5rpx;
}

.select-all {
	display: flex;
	align-items: center;
}

.select-all-text {
	font-size: 28rpx;
	color: #333333;
	margin-left: 10rpx;
}

/* 空状态样式 */
.empty-state {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	// padding: 100rpx 40rpx;
	min-height: calc(100vh - 200rpx); /* 确保有足够高度进行垂直居中 */
}

.empty-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 40rpx;
	width: 100%;
	max-width: 500rpx; /* 限制最大宽度 */
	margin: 0 auto; /* 水平居中 */
}

.action-button {
	width: 500rpx;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	position: relative;
	background: transparent;
}

.action-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	// background: linear-gradient(135deg, #8B0000 0%, #A52A2A 100%);
	z-index: 1;
}
.button-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
	border-radius: 50rpx;
}
.action-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	position: relative;
	z-index: 2;
}

/* 人员列表样式 */
.people-list {
	padding: 20rpx;
	flex: 1;
}

.person-item {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.person-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.desk-number {
	font-size: 30rpx;
	font-weight: 500;
	color: #333333;
}

.person-details {
	padding: 30rpx;
}

.detail-row {
	display: flex;
	margin-bottom: 20rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-item {
	flex: 1;
	display: flex;
	align-items: center;
}

.detail-label {
	font-size: 26rpx;
	color: #666666;
	width: 126rpx;
}

.detail-value {
	font-size: 26rpx;
	color: #333333;
	flex: 1;
}

.person-actions {
	display: flex;
	justify-content: flex-end;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 15rpx;
}

.action-btn {
	padding: 0 25rpx;
	height: 56rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	box-shadow: 0 2rpx 5rpx rgba(0,0,0,0.1);
}

.delete-btn {
	background-color: #ffffff;
	border: 1rpx solid #cccccc;
	color: #555555;
}

.edit-btn {
	background-color: #8B0000;
	color: #ffffff;
}

/* 浮动按钮样式 */
.floating-buttons {
	position: fixed;
	right: 30rpx;
	bottom: 200rpx;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	z-index: 100;
}

.float-btn {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.add-btn {
	background-color: #8B0000;
}

.float-btn-text {
	font-size: 40rpx;
	font-weight: 300;
	color: #ffffff;
}

.excel-btn {
	background-color: #8B0000;
}

.excel-text {
	font-size: 20rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 底部删除按钮 */
.bottom-action {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	background-color: #ffffff;
	border-top: 1rpx solid #f0f0f0;
	z-index: 99;
}

.delete-selected-btn {
	width: 100%;
	height: 80rpx;
	background-color: #8B0000;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 500;
	color: #ffffff;
	box-shadow: 0 4rpx 12rpx rgba(139, 0, 0, 0.3);
}

/* 会议室选择弹窗样式 */
.room-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 1000;
	transition: all 0.3s ease;
}

.room-modal-content {
	width: 100%;
	max-height: 70vh;
	background-color: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.2);
	transform: translateY(0);
	transition: transform 0.3s ease;
	animation: slideUp 0.3s ease;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	color: #666666;
	font-size: 36rpx;
	cursor: pointer;
}

.close-btn:hover {
	background-color: #e0e0e0;
}

.room-list {
	max-height: 600rpx;
	overflow-y: auto;
}

.room-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s;
}

.room-option:last-child {
	border-bottom: none;
}

.room-option:hover {
	background-color: #f8f8f8;
}

.room-option-name {
	font-size: 30rpx;
	color: #333333;
}

.room-option-check {
	width: 40rpx;
	height: 40rpx;
	border-radius: 20rpx;
	background-color: #8B0000;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
		font-weight: bold;
	}

/* 新增/编辑人员弹窗样式 */
.person-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1001;
	transition: all 0.3s ease;
}

.person-modal-content {
	width: 90%;
	max-width: 700rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
	transform: scale(1);
	transition: transform 0.3s ease;
	animation: modalShow 0.3s ease;
}

@keyframes modalShow {
	from {
		transform: scale(0.8);
		opacity: 0;
	}
	to {
		transform: scale(1);
		opacity: 1;
	}
}

.person-form {
	padding: 30rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.form-item {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 8rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #666666;
	width: 160rpx;
	flex-shrink: 0;
}

.form-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	background-color: transparent;
	border: none;
	outline: none;
}

.form-input::placeholder {
	color: #999999;
}

.modal-actions {
	display: flex;
	justify-content: space-between;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 30rpx;
}

.modal-actions .action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cancel-btn {
	background-color: #f5f5f5;
	color: #666666;
	border: 1rpx solid #e0e0e0;
}

.cancel-btn:hover {
	background-color: #e8e8e8;
}

.confirm-btn {
	background-color: #8B0000;
	color: #ffffff;
	border: 1rpx solid #8B0000;
}

.confirm-btn:hover {
		background-color: #A52A2A;
	}

/* 桌牌选择下拉框样式 */
.form-select {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
	background-color: transparent;
	border: none;
	outline: none;
	cursor: pointer;
	transition: all 0.3s ease;
}

.form-select:hover {
	background-color: #f0f0f0;
}

.select-text {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
}

.select-text.placeholder {
	color: #999999;
}

.select-arrow {
	font-size: 20rpx;
	color: #666666;
	transition: transform 0.3s ease;
}

/* 桌牌选择弹窗样式 */
.desk-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 1002;
	transition: all 0.3s ease;
}

.desk-modal-content {
	width: 100%;
	max-width: 100%;
	max-height: 70vh;
	background-color: #ffffff;
	border-radius: 16rpx 16rpx 0 0;
	overflow: hidden;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.2);
	transform: translateY(0);
	transition: transform 0.3s ease;
	animation: slideUpDesk 0.3s ease;
}

@keyframes slideUpDesk {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.desk-list {
	padding: 20rpx;
	max-height: 50vh;
	overflow-y: auto;
}

.desk-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	margin-bottom: 16rpx;
	background-color: #f8f8f8;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.desk-option:hover {
	background-color: #e8e8e8;
}

.desk-option.disabled {
	background-color: #f5f5f5;
	opacity: 0.6;
	cursor: not-allowed;
}

.desk-option.disabled:hover {
	background-color: #f5f5f5;
}

.desk-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.desk-number {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.desk-gateway {
	font-size: 24rpx;
	color: #666666;
}

.desk-status {
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	align-self: flex-start;
}

.desk-status.available {
	background-color: #e8f5e8;
	color: #4caf50;
}

.desk-status.bound {
	background-color: #fff3e0;
	color: #ff9800;
}

.desk-status.offline {
	background-color: #ffebee;
	color: #f44336;
}

.desk-check {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background-color: #8B0000;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
}
</style>
