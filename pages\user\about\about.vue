<template>
	<view class="container">
		<view class="header">
			<image src="http://*************:8890/i/2025/06/12/visitor_title.png" class="visitor-title" mode="aspectFit"></image>
		</view>
		
		<view class="content">
			<view class="description">
				<text class="desc-text">智能电子桌牌是通过手机APP和电脑管理软件操作，</text>
				<text class="desc-text">无线通讯技术传输，将需求显示的内容，</text>
				<text class="desc-text">对于参会人员较多的场景，免去了</text>
				<text class="desc-text">自动刷新显示的一款新型会议桌牌，</text>
				<text class="desc-text">一键批量下发至终端。</text>
			</view>
			
			<view class="description">
				<text class="desc-text">该产品摆弃了纸质书写会议内容的传统桌牌，</text>
				<text class="desc-text">实现了一次摆放，重复使用，批量导入，快速修改</text>
				<text class="desc-text">的愿景。</text>
			</view>
			
			<view class="description">
				<text class="desc-text">人工书写，制作桌牌的方式，节省了大量的人力物</text>
				<text class="desc-text">力，</text>
			</view>
			
			<view class="description">
				<text class="desc-text">同时实现了真正的无纸化办公，</text>
			</view>
			
			<view class="description">
				<text class="desc-text">达到了节能，环保，便捷的使用特点。</text>
			</view>
			
			<view class="features">
				<view class="feature-item">
					<view class="feature-circle">
						<text class="feature-text">纸质</text>
						<text class="feature-text">效果</text>
					</view>
				</view>
				<view class="feature-item">
					<view class="feature-circle">
						<text class="feature-text">便携</text>
						<text class="feature-text">高效</text>
					</view>
				</view>
				<view class="feature-item">
					<view class="feature-circle">
						<text class="feature-text">提升</text>
						<text class="feature-text">格调</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="footer">
			<button class="login-button" @click="goToLogin">返回登录</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			goToLogin() {
				uni.navigateTo({
					url: '/pages/user/login'
				});
			}
		}
	}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-height: 100vh;
	background-image: url('http://*************:8890/i/2025/06/12/login-bg.png');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center;
	padding: 40px 20px;
	padding-bottom: calc(20px + env(safe-area-inset-bottom));
	box-sizing: border-box;
}

.header {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	margin-bottom: 30px;
}

.visitor-title {
	width: 300px;
	height: 80px;
}

.content {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	max-width: 400px;
	padding: 0 20px;
	margin-bottom: 20px;
}

.description {
	margin-bottom: 15px;
	text-align: center;
	line-height: 1.6;
}

.desc-text {
	font-size: 16px;
	color: #333;
	line-height: 1.8;
}

.features {
	display: flex;
	justify-content: space-around;
	width: 100%;
	margin-top: 40px;
	gap: 20px;
}

.feature-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.feature-circle {
	width: 80px;
	height: 80px;
	border: 2px solid #8B4513;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: rgba(255, 255, 255, 0.8);
}

.feature-text {
	font-size: 14px;
	color: #8B4513;
	font-weight: bold;
	line-height: 1.2;
}

.footer {
	width: 100%;
	max-width: 300px;
	padding: 20px 0;
	margin-top: auto;
}

.login-button {
	width: 100%;
	height: 48px;
	line-height: 48px;
	border-radius: 8px;
	font-size: 16px;
	text-align: center;
	color: #fff;
	border: none;
	background-color: #8B4513;
}
</style>
