<template>
	<view class="container"> 
		<!-- 主内容区域 -->
		<view class="content">
			<!-- 屏幕模式选择 -->
			<view class="screen-mode-tabs">
				<view class="tab-row">
					<view class="tab-item" :class="{active: screenMode === 'same'}" @click="switchScreenMode('same')" style="width: 50%;">
						<text>前后屏相同</text>
					</view>
					<view class="tab-item" :class="{active: screenMode === 'different'}" @click="switchScreenMode('different')" style="width: 50%;">
						<text>前后屏不同</text>
					</view>
				</view>
			</view>
			
			<!-- 预览区域 -->
			<view class="preview-area">
				<!-- 前后屏相同模式 -->
				<view class="preview-card" v-if="screenMode === 'same'">
					<view class="template-preview">
						<!-- 中国风边框和内容 -->
						<view class="chinese-style-template front-screen" :style="{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)'}">
							<view class="screen-tag">前</view>
							 
						</view>
						
						<view class="operation-buttons">
							<view class="op-button-row">
								<view class="op-button" @click="showTemplateSelector" style="width: 50%;">
									<image class="op-icon" src="/static/images/template-icon.svg" mode="aspectFit"></image>
									<text class="op-text">选择模板</text>
								</view>
								<view class="op-button" @click="showContentEditor" style="width: 50%;">
									<image class="op-icon" src="/static/images/edit-icon.svg" mode="aspectFit"></image>
									<text class="op-text">编辑内容</text>
								</view>
							</view>
							<view class="bottom-gradient-line"></view>
						</view>
					</view>
				</view>
				
				<!-- 前后屏不同模式 -->
				<view class="preview-cards" v-if="screenMode === 'different'">
					<view>
						<!-- 前屏 -->
						<view class="preview-card">
							<view class="template-preview">
								<view class="chinese-style-template front-screen" :style="{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png)'}">
									<view class="screen-tag">前</view> 
								</view>
							</view>
						</view>
						<!-- 按钮模块 -->
						<view class="operation-buttons">
							<view class="op-button-row">
								<view class="op-button" @click="showTemplateSelector" style="width: 50%;">
									<image class="op-icon" src="/static/images/template-icon.svg" mode="aspectFit"></image>
									<text class="op-text">选择模板</text>
								</view>
								<view class="op-button" @click="showContentEditor" style="width: 50%;">
									<image class="op-icon" src="/static/images/edit-icon.svg" mode="aspectFit"></image>
									<text class="op-text">编辑内容</text>
								</view>
							</view>
							<view class="bottom-gradient-line"></view>
						</view>
					</view>
					<view>
						<!-- 后屏 -->
						<view class="preview-card">
							<view class="template-preview">
								<view class="chinese-style-template back-screen" :style="{'background-image': currentTemplate && currentTemplate.background ? 'url(' + currentTemplate.background + ')' : 'url(/static/images/public_template/f2e05295636f1b849b791a21b5548e30.png)'}">
									<view class="screen-tag">后</view> 
								</view>
							</view>
						</view>
						<!-- 按钮模块 -->
						<view class="operation-buttons">
							<view class="op-button-row">
								<view class="op-button" @click="showTemplateSelector" style="width: 50%;">
									<image class="op-icon" src="/static/images/template-icon.svg" mode="aspectFit"></image>
									<text class="op-text">选择模板</text>
								</view>
								<view class="op-button" @click="showContentEditor" style="width: 50%;">
									<image class="op-icon" src="/static/images/edit-icon.svg" mode="aspectFit"></image>
									<text class="op-text">编辑内容</text>
								</view>
							</view>
							<view class="bottom-gradient-line"></view>
						</view>
					</view> 
				</view>
			</view> 
		</view>
		
		<!-- 底部确认按钮 -->
		<view class="footer">
			<view class="confirm-button" @click="confirmScreen">
				<text>确认无误，下一步</text>
			</view>
		</view>
		
		<!-- 模板选择弹窗 -->
		<u-popup :show="showTemplates" mode="bottom" @close="closeTemplateSelector" round="10" closeable>
			<view class="template-popup">
				<view class="popup-title">
					<text>选择模板</text>
				</view>
				<scroll-view class="template-scroll" scroll-x="true" show-scrollbar="false">
					<view class="template-list">
						<view class="template-item" v-for="(item, index) in templates" :key="index" @click="selectTemplate(item)" :class="{active: selectedTemplate === item.id}">
							<image class="template-image" :src="item.image" mode="aspectFill"></image>
							<text class="template-name">{{item.name}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- 内容编辑弹窗 -->
		<u-popup :show="showEditor" mode="bottom" @close="closeContentEditor" round="10" closeable>
			<view class="editor-popup">
				<view class="popup-title">
					<text>编辑内容</text>
				</view>
				<view class="edit-form">
					<view class="form-item" v-for="(field, index) in editFields" :key="index">
						<text class="form-label">{{field.label}}</text>
						<input class="form-input" v-model="field.value" :placeholder="field.placeholder" />
					</view>
				</view>
				<view class="popup-button" @click="saveContent">
					<text>保存</text>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { fetchTemplates, fetchDeviceTemplate } from '@/common/api.js';

	export default {
		data() {
			return {
				isMP: false, // 是否为小程序环境
				screenMode: 'same', // 屏幕模式：same-前后屏相同，different-前后屏不同
				showTemplates: false, // 是否显示模板选择弹窗
				showEditor: false, // 是否显示内容编辑弹窗
				deviceInfo: {
					name: '设备名称',
					id: '12345678',
					mac: '00:11:22:33:44:55',
					status: 'connected'
				},
				templates: [],
				selectedTemplate: null,
				currentTemplate: null,
				loading: false,
				editFields: [
					{
						label: '姓名',
						value: '',
						placeholder: '请输入姓名'
					},
					{
						label: '职称',
						value: '',
						placeholder: '请输入职称'
					},
					{
						label: '公司名称',
						value: '',
						placeholder: '请输入公司名称'
					},
					{
						label: '后屏信息1',
						value: '',
						placeholder: '请输入后屏信息1'
					},
					{
						label: '后屏信息2',
						value: '',
						placeholder: '请输入后屏信息2'
					}
				]
			}
		},
		onLoad(options) {
			// 获取设备信息
			if (options.deviceId) {
				this.deviceInfo.id = options.deviceId;
				// 获取设备详情
				this.getDeviceInfo(options.deviceId);
				// 获取设备当前使用的模板
				this.getDeviceTemplate(options.deviceId);
			}
			
			// 获取所有模板列表
			this.getTemplateList();
			
			// 判断平台
			// #ifdef MP
			this.isMP = true;
			// #endif
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			
			// 获取设备详情
			getDeviceInfo(deviceId) {
				// 这里应该调用API获取设备详情
				// 模拟API调用
				setTimeout(() => {
					this.deviceInfo = {
						name: '电子桌牌-' + deviceId,
						id: deviceId,
						mac: '00:11:22:33:44:55',
						status: 'connected'
					};
				}, 500);
			},
			
			// 获取模板列表
			getTemplateList() {
				this.loading = true;
				// 从本地temp.json文件加载模板数据
				uni.request({
					url: '/pages/public/tempjson/temp.json',
					method: 'GET',
					success: (res) => {
						if (res.data && res.data.code === 200 && res.data.data) {
							// 转换数据格式
							this.templates = res.data.data.map(item => {
								return {
									id: item.id,
									name: item.template.name || `模板${item.id}`,
									image: item.template.path || '/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png',
									frontImage: '/static/images/bamboo.svg',
									backImage: '/static/images/lantern.svg',
									background: item.background,
									type: 'chinese',
									fields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']
								};
							});
							
							// 如果没有选中模板，默认选择第一个
							if (!this.selectedTemplate && this.templates.length > 0) {
								this.selectedTemplate = this.templates[0].id;
								this.currentTemplate = this.templates[0];
							}
						} else {
							console.error('模板数据格式不正确');
							// 使用默认模拟数据
							this.useDefaultTemplates();
						}
					},
					fail: (err) => {
						console.error('获取模板列表失败', err);
						// 使用默认模拟数据
						this.useDefaultTemplates();
					},
					complete: () => {
						this.loading = false;
					}
				});
			},
			
			// 使用默认模拟数据
			useDefaultTemplates() {
				this.templates = [
					{
						id: 1,
						name: '中国风红色',
						image: '/static/images/public_template/33882e8f90e990a4f1f140011e7a58ce.png',
						frontImage: '/static/images/bamboo.svg',
						backImage: '/static/images/lantern.svg',
						background: 'http://www.chuantiba.com/api/storage/public/1633881600/33882e8f90e990a4f1f140011e7a58ce.png',
						type: 'chinese',
						fields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']
					},
					{
						id: 2,
						name: '中国风黑色',
						image: '/static/images/public_template/f2e05295636f1b849b791a21b5548e30.png',
						frontImage: '/static/images/bamboo.svg',
						backImage: '/static/images/lantern.svg',
						background: 'http://www.chuantiba.com/api/storage/public/1633881600/f2e05295636f1b849b791a21b5548e30.png',
						type: 'chinese',
						fields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']
					}
				];
				
				// 如果没有选中模板，默认选择第一个
				if (!this.selectedTemplate && this.templates.length > 0) {
					this.selectedTemplate = this.templates[0].id;
					this.currentTemplate = this.templates[0];
				}
			},
			
			// 获取设备当前使用的模板
			getDeviceTemplate(deviceId) {
				// 调用API获取设备当前使用的模板
				fetchDeviceTemplate(deviceId)
					.then(res => {
						if (res.code === 200 && res.data) {
							this.selectedTemplate = res.data.templateId;
							this.screenMode = res.data.screenMode || 'same';
							// 更新编辑字段
							if (res.data.content && res.data.content.length > 0) {
								res.data.content.forEach((item, index) => {
									if (this.editFields[index]) {
										this.editFields[index].value = item.value;
									}
								});
							}
						}
					})
					.catch(err => {
						console.error('获取设备模板失败', err);
					});
			},
			// 切换屏幕模式
			switchScreenMode(mode) {
				this.screenMode = mode;
				uni.showToast({
					title: mode === 'same' ? '已选择前后屏相同' : '已选择前后屏不同',
					icon: 'none',
					duration: 1500
				});
			},
			// 显示模板选择器
			showTemplateSelector() {
				this.showTemplates = true;
			},
			// 关闭模板选择器
			closeTemplateSelector() {
				this.showTemplates = false;
			},
			// 显示内容编辑器
			showContentEditor() {
				// 从temp.json加载模板数据
				uni.request({
					url: '/pages/public/tempjson/temp.json',
					method: 'GET',
					success: (res) => {
						if (res.data && res.data.code === 200 && res.data.data && res.data.data.length > 0) {
							// 获取第一个模板的ID和背景
							const templateId = res.data.data[0].id;
							const templateBackground = res.data.data[0].background;
							
							// 更新当前模板和选中模板
							if (!this.currentTemplate) {
								this.currentTemplate = {
									id: templateId,
									background: templateBackground,
									frontImage: '/static/images/bamboo.svg',
									backImage: '/static/images/lantern.svg',
									type: 'chinese',
									fields: ['姓名', '职称', '公司名称', '后屏信息1', '后屏信息2']
								};
							} else {
								// 更新现有模板的背景
								this.currentTemplate.background = templateBackground;
							}
							
							this.selectedTemplate = templateId;
						}
					},
					fail: (err) => {
						console.error('加载模板数据失败', err);
					},
					complete: () => {
						// 显示编辑器弹窗
						this.showEditor = true;
					}
				});
			},
			// 关闭内容编辑器
			closeContentEditor() {
				this.showEditor = false;
			},
			// 保存编辑内容
			saveContent() {
				this.showEditor = false;
				uni.showToast({
					title: '内容已保存',
					icon: 'success'
				});
			},
			// 选择模板
			selectTemplate(template) {
				this.selectedTemplate = template.id;
				this.currentTemplate = template;
				this.showTemplates = false;
				uni.showToast({
					title: `已选择${template.name}模板`,
					icon: 'success'
				});
				
				// 根据模板更新编辑字段
				if (template.fields && template.fields.length > 0) {
					const newEditFields = [];
					template.fields.forEach((field, index) => {
						// 保留已有的值
						const existingValue = this.editFields[index] ? this.editFields[index].value : '';
						newEditFields.push({
							label: field,
							value: existingValue,
							placeholder: `请输入${field}`
						});
					});
					this.editFields = newEditFields;
				}
			},
			// 确认投屏
			confirmScreen() {
				// 验证表单
				let isValid = true;
				for (let field of this.editFields) {
					if (!field.value) {
						isValid = false;
						uni.showToast({
							title: `请输入${field.label}`,
							icon: 'none'
						});
						break;
					}
				}
				
				if (isValid) {
					// 构建投屏数据
					const screenData = {
						deviceId: this.deviceInfo.id,
						templateId: this.selectedTemplate,
						screenMode: this.screenMode,
						content: this.editFields.map(field => ({
							label: field.label,
							value: field.value
						}))
					};
					
					// 发送投屏请求
					console.log('投屏数据:', screenData);
					uni.showLoading({
						title: '投屏中...'
					});
					
					// 模拟投屏请求
					setTimeout(() => {
						uni.hideLoading();
						uni.showToast({
							title: '投屏成功',
							icon: 'success'
						});
						
						// 投屏成功后返回
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}, 2000);
				}
			}
		}
	}
</script>

<style lang="scss">
.container {
	flex: 1;
	background-color: #f5f5f5;
	background-image: url('/static/images/bg.svg');
	background-size: cover;
	background-position: center;
}

.navbar {
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 40rpx;
	padding-top: calc(20rpx + var(--status-bar-height));
	background-color: transparent;
}

.left-capsule {
	width: 80rpx;
}

.capsule-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	background-color: rgba(255, 255, 255, 0.8);
	justify-content: center;
	align-items: center;
}

.nav-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #ffffff;
}

.right-capsule {
	width: 80rpx;
}

.content {
	padding: 30rpx;
	flex: 1;
}

/* 屏幕模式选择标签 */
.screen-mode-tabs {
	background-color: #8B0000; /* 深红色背景 */
	border-radius: 8rpx;
	overflow: hidden;
	margin-bottom: 30rpx;
}

.tab-row {
	display: flex;
	flex-direction: row;
	width: 100%;
}

.tab-item {
	height: 80rpx;
	justify-content: center;
	align-items: center;
	display: flex;
}

.tab-item text {
	color: #ffffff;
	font-size: 28rpx;
}

.tab-item.active {
	background-color: #FF0000; /* 鲜红色 */
}

/* 预览区域 */
.preview-area {
	margin-bottom: 30rpx;
}

.preview-card {
	background-color: #ffffff;
	// border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	//margin-bottom: 20rpx;
}

.template-preview {
	padding: 20rpx;
	align-items: center;
	justify-content: center;
}

/* 前后屏不同模式的卡片布局 */
.preview-cards {
	display: flex;
	flex-direction: column;
}

/* 中国风模板样式 */
.chinese-style-template {
	width: 100%;
	height: 400rpx;
	position: relative;
	border: 2rpx solid #000000;
	border-radius: 8rpx;
	overflow: hidden;
	background-color: #ffffff;
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

/* 前后屏标签 */
.screen-tag {
	position: absolute;
	top: 0;
	left: 0;
	width: 60rpx;
	height: 60rpx;
	background-color: #8B0000;
	color: #ffffff;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 28rpx;
	font-weight: bold;
	border-bottom-right-radius: 8rpx;
}
 
.template-content {
	width: 100%;
	height: 100%;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	background-color: rgba(255, 255, 255, 0.7);
	border-radius: 8rpx;
	margin: 20rpx;
}

/* 姓名和职称容器 */
.name-title-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.for-front-screen {
	// display: flex;
	// flex-direction: column;
	width: 100%; /* 或指定具体的宽度 */
    height: auto; /* 保持图片的原始比例 */
    object-fit: cover; /* 覆盖容器，可能会裁剪图片 */
}


.content-divider {
	font-size: 60rpx;
	font-weight: bold;
	margin: 0 20rpx;
}

.content-title {
	font-size: 60rpx;
	font-weight: bold;
	text-shadow: 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.company-name {
	font-size: 36rpx;
	color: #FF0000;
	background: linear-gradient(to right, #FF0000, #000000);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	padding: 10rpx 0;
	text-shadow: 0 0 2rpx rgba(255, 255, 255, 0.5);
}

.company-name-small {
	font-size: 28rpx;
	color: #333;
	margin-top: 10rpx;
	font-weight: bold;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.content-subtitle {
	font-size: 36rpx;
	color: #333;
	font-weight: bold;
	text-shadow: 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 底部操作按钮 */
.operation-buttons {
	margin-bottom: 30rpx;
	background-color: #ffffff;
	// border-radius: 16rpx;
	padding: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	position: relative;
}

.op-button-row {
	display: flex;
	flex-direction: row;
	width: 100%;
	position: relative;
}

/* 删除button-divider样式 */

.bottom-gradient-line {
	height: 4rpx;
	width: 100%;
	background: linear-gradient(to right, #8B0000, #000000);
	position: absolute;
	bottom: 0;
	left: 0;
	border-bottom-left-radius: 16rpx;
	border-bottom-right-radius: 16rpx;
}
.op-button {
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	display: flex;
	flex-direction: column;
}

.op-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
}

.op-text {
	font-size: 28rpx;
	color: #333;
}

/* 底部确认按钮 */
.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: transparent;
	padding: 20rpx 30rpx;
	// padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
}

.confirm-button {
	background-color: #8B0000; /* 深红色 */
	height: 90rpx;
	border-radius: 45rpx;
	justify-content: center;
	align-items: center;
	box-shadow: 0 4rpx 10rpx rgba(139, 0, 0, 0.3);
	display: flex;
	width: 80%;
}

.confirm-button text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
	align-items: center;
}

/* 弹窗样式 */
.template-popup, .editor-popup {
	padding: 30rpx;
	background-color: #ffffff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	text-align: center;
}

.template-scroll {
	width: 100%;
	height: 300rpx;
}

.template-list {
	flex-direction: row;
	padding: 10rpx 0;
}

.template-item {
	width: 200rpx;
	margin-right: 20rpx;
	align-items: center;
}

.template-image {
	width: 180rpx;
	height: 240rpx;
	border-radius: 8rpx;
	border: 2rpx solid #eee;
}

.template-item.active .template-image {
	border: 2rpx solid #8B0000;
}

.template-name {
	font-size: 24rpx;
	margin-top: 10rpx;
	text-align: center;
}

.edit-form {
	margin-top: 20rpx;
	margin-bottom: 30rpx;
}

.form-item {
	margin-bottom: 20rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
}

.form-input {
	height: 80rpx;
	border: 1px solid #ddd;
	border-radius: 8rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}

.popup-button {
	background-color: #8B0000; /* 深红色 */
	height: 80rpx;
	border-radius: 40rpx;
	justify-content: center;
	align-items: center;
}

.popup-button text {
	color: #ffffff;
	font-size: 30rpx;
	font-weight: bold;
}
</style>