import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

let app_user_token = "app_user_token";

// 用户状态管理（保持原有功能）
const store = new Vuex.Store({
  state: {
    test: '',
    permission: [],
    userInfo: null
  },
  
  mutations: {
    SET_TEST(state, value) {
      state.test = value;
    },
    
    SET_PERMISSION(state, data) {
      if (!data || typeof data != 'object') return;
      if (data instanceof Array) {
        state.permission.push(...data);
      } else {
        state.permission = data;
      }
    },
    
    SET_USER_INFO(state, data) {
      state.userInfo = data;
      uni.setStorageSync(app_user_token, JSON.stringify(data));
    },
    
    CLEAR_USER_INFO(state) {
      state.permission = [];
      state.userInfo = null;
      uni.clearStorage(app_user_token);
    }
  },
  
  actions: {
    setCountInc({ commit }, value) {
      commit('SET_TEST', value);
    },
    
    setPermission({ commit }, data) {
      commit('SET_PERMISSION', data);
    },
    
    setUserInfo({ commit }, data) {
      commit('SET_USER_INFO', data);
    },
    
    clearUserInfo({ commit }) {
      commit('CLEAR_USER_INFO');
    },
    
    getUserInfo({ commit, state }) {
      if (state.userInfo) return state.userInfo;
      let userInfo = uni.getStorageSync(app_user_token);
      if (userInfo) {
        const parsedUserInfo = JSON.parse(userInfo);
        commit('SET_USER_INFO', parsedUserInfo);
        return parsedUserInfo;
      }
      return state.userInfo;
    }
  },
  
  getters: {
    test: (state) => state.test,
    
    getPermission: (state) => (path) => {
      if (!path) return state.permission;
      return state.permission.find(x => x.path == path);
    },
    
    getMenu: (state) => {
      return state.permission || [];
    },
    
    getUserInfo: (state) => {
      return state.userInfo;
    },
    
    getUserName: (state) => {
      if (state.userInfo) {
        return state.userInfo.userName;
      }
      return '未获取到登陆信息';
    },
    
    getToken: (state) => {
      if (state.userInfo) {
        return 'Bearer ' + state.userInfo.token;
      }
      return '';
    },
    
    isLogin: (state) => {
      return !!state.userInfo;
    }
  }
})

export default store
